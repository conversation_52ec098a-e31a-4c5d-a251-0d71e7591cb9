int attr alpha 0x7f030028
int attr font 0x7f030166
int attr fontProviderAuthority 0x7f030168
int attr fontProviderCerts 0x7f030169
int attr fontProviderFetchStrategy 0x7f03016a
int attr fontProviderFetchTimeout 0x7f03016b
int attr fontProviderPackage 0x7f03016c
int attr fontProviderQuery 0x7f03016d
int attr fontStyle 0x7f03016f
int attr fontVariationSettings 0x7f030170
int attr fontWeight 0x7f030171
int attr ttcIndex 0x7f03036c
int bool enable_system_alarm_service_default 0x7f040003
int bool enable_system_foreground_service_default 0x7f040004
int bool enable_system_job_service_default 0x7f040005
int bool workmanager_test_configuration 0x7f040007
int color notification_action_color_filter 0x7f0500b3
int color notification_icon_bg_color 0x7f0500b4
int color ripple_material_light 0x7f0500bf
int color secondary_text_default_material_light 0x7f0500c1
int dimen compat_button_inset_horizontal_material 0x7f060054
int dimen compat_button_inset_vertical_material 0x7f060055
int dimen compat_button_padding_horizontal_material 0x7f060056
int dimen compat_button_padding_vertical_material 0x7f060057
int dimen compat_control_corner_material 0x7f060058
int dimen compat_notification_large_icon_max_height 0x7f060059
int dimen compat_notification_large_icon_max_width 0x7f06005a
int dimen notification_action_icon_size 0x7f060180
int dimen notification_action_text_size 0x7f060181
int dimen notification_big_circle_margin 0x7f060182
int dimen notification_content_margin_start 0x7f060183
int dimen notification_large_icon_height 0x7f060184
int dimen notification_large_icon_width 0x7f060185
int dimen notification_main_column_padding_top 0x7f060186
int dimen notification_media_narrow_margin 0x7f060187
int dimen notification_right_icon_size 0x7f060188
int dimen notification_right_side_padding_top 0x7f060189
int dimen notification_small_icon_background_padding 0x7f06018a
int dimen notification_small_icon_size_as_large 0x7f06018b
int dimen notification_subtext_size 0x7f06018c
int dimen notification_top_pad 0x7f06018d
int dimen notification_top_pad_large_text 0x7f06018e
int drawable notification_action_background 0x7f070086
int drawable notification_bg 0x7f070087
int drawable notification_bg_low 0x7f070088
int drawable notification_bg_low_normal 0x7f070089
int drawable notification_bg_low_pressed 0x7f07008a
int drawable notification_bg_normal 0x7f07008b
int drawable notification_bg_normal_pressed 0x7f07008c
int drawable notification_icon_background 0x7f07008d
int drawable notification_template_icon_bg 0x7f07008e
int drawable notification_template_icon_low_bg 0x7f07008f
int drawable notification_tile_bg 0x7f070090
int drawable notify_panel_notification_icon_bg 0x7f070091
int id accessibility_action_clickable_span 0x7f08000f
int id accessibility_custom_action_0 0x7f080010
int id accessibility_custom_action_1 0x7f080011
int id accessibility_custom_action_10 0x7f080012
int id accessibility_custom_action_11 0x7f080013
int id accessibility_custom_action_12 0x7f080014
int id accessibility_custom_action_13 0x7f080015
int id accessibility_custom_action_14 0x7f080016
int id accessibility_custom_action_15 0x7f080017
int id accessibility_custom_action_16 0x7f080018
int id accessibility_custom_action_17 0x7f080019
int id accessibility_custom_action_18 0x7f08001a
int id accessibility_custom_action_19 0x7f08001b
int id accessibility_custom_action_2 0x7f08001c
int id accessibility_custom_action_20 0x7f08001d
int id accessibility_custom_action_21 0x7f08001e
int id accessibility_custom_action_22 0x7f08001f
int id accessibility_custom_action_23 0x7f080020
int id accessibility_custom_action_24 0x7f080021
int id accessibility_custom_action_25 0x7f080022
int id accessibility_custom_action_26 0x7f080023
int id accessibility_custom_action_27 0x7f080024
int id accessibility_custom_action_28 0x7f080025
int id accessibility_custom_action_29 0x7f080026
int id accessibility_custom_action_3 0x7f080027
int id accessibility_custom_action_30 0x7f080028
int id accessibility_custom_action_31 0x7f080029
int id accessibility_custom_action_4 0x7f08002a
int id accessibility_custom_action_5 0x7f08002b
int id accessibility_custom_action_6 0x7f08002c
int id accessibility_custom_action_7 0x7f08002d
int id accessibility_custom_action_8 0x7f08002e
int id accessibility_custom_action_9 0x7f08002f
int id action_container 0x7f080037
int id action_divider 0x7f080039
int id action_image 0x7f08003a
int id action_text 0x7f080040
int id actions 0x7f080041
int id async 0x7f08004c
int id blocking 0x7f080055
int id chronometer 0x7f080066
int id dialog_button 0x7f080083
int id forever 0x7f0800a6
int id icon 0x7f0800b4
int id icon_group 0x7f0800b5
int id info 0x7f0800ba
int id italic 0x7f0800bd
int id line1 0x7f0800c5
int id line3 0x7f0800c6
int id normal 0x7f080108
int id notification_background 0x7f080109
int id notification_main_column 0x7f08010a
int id notification_main_column_container 0x7f08010b
int id right_icon 0x7f080126
int id right_side 0x7f080127
int id tag_accessibility_actions 0x7f080161
int id tag_accessibility_clickable_spans 0x7f080162
int id tag_accessibility_heading 0x7f080163
int id tag_accessibility_pane_title 0x7f080164
int id tag_screen_reader_focusable 0x7f080168
int id tag_transition_group 0x7f08016a
int id tag_unhandled_key_event_manager 0x7f08016b
int id tag_unhandled_key_listeners 0x7f08016c
int id text 0x7f080172
int id text2 0x7f080173
int id time 0x7f080182
int id title 0x7f080183
int id unitySurfaceView 0x7f080194
int id view_tree_view_model_store_owner 0x7f080199
int integer status_bar_notification_info_maxnum 0x7f09001b
int layout custom_dialog 0x7f0b001c
int layout notification_action 0x7f0b0057
int layout notification_action_tombstone 0x7f0b0058
int layout notification_template_custom_big 0x7f0b0059
int layout notification_template_icon_group 0x7f0b005a
int layout notification_template_part_chronometer 0x7f0b005b
int layout notification_template_part_time 0x7f0b005c
int mipmap app_icon 0x7f0c0000
int mipmap app_icon_round 0x7f0c0001
int mipmap ic_launcher_background 0x7f0c0002
int mipmap ic_launcher_foreground 0x7f0c0003
int string androidx_startup 0x7f0e001b
int string app_name 0x7f0e001c
int string game_view_content_description 0x7f0e0029
int string status_bar_notification_info_overflow 0x7f0e006a
int style BaseUnityTheme 0x7f0f00e4
int style TextAppearance_Compat_Notification 0x7f0f0162
int style TextAppearance_Compat_Notification_Info 0x7f0f0163
int style TextAppearance_Compat_Notification_Line2 0x7f0f0164
int style TextAppearance_Compat_Notification_Time 0x7f0f0165
int style TextAppearance_Compat_Notification_Title 0x7f0f0166
int style UnityThemeSelector 0x7f0f0204
int style UnityThemeSelector_Translucent 0x7f0f0205
int style Widget_Compat_NotificationActionContainer 0x7f0f024f
int style Widget_Compat_NotificationActionText 0x7f0f0250
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x7f030028 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_alpha 2
int[] styleable FontFamily { 0x7f030168, 0x7f030169, 0x7f03016a, 0x7f03016b, 0x7f03016c, 0x7f03016d }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f030166, 0x7f03016f, 0x7f030170, 0x7f030171, 0x7f03036c }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int xml file_paths 0x7f110000
