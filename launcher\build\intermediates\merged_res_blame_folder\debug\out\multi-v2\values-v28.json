{"logs": [{"outputFile": "E:\\huoli-rk3566\\hl071302\\launcher\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-v28\\values-v28.xml", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\0afbca5d75ed49db5552569b9dce4707\\material-1.4.0\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,143,231,319,407,494,581,668", "endColumns": "87,87,87,87,86,86,86,86", "endOffsets": "138,226,314,402,489,576,663,750"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\f9db3af1d4b21c0f8eaed47c1558f861\\appcompat-1.2.0\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,8", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,447", "endLines": "2,3,7,11", "endColumns": "74,86,12,12", "endOffsets": "125,212,442,684"}, "to": {"startLines": "10,11,12,16", "startColumns": "4,4,4,4", "startOffsets": "755,830,917,1147", "endLines": "10,11,15,19", "endColumns": "74,86,12,12", "endOffsets": "825,912,1142,1384"}}, {"source": "E:\\huoli-rk3566\\hl071302\\launcher\\src\\main\\res\\values-v28\\styles.xml", "from": {"startLines": "2", "startColumns": "0", "startOffsets": "53", "endLines": "4", "endColumns": "8", "endOffsets": "229"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1389", "endLines": "22", "endColumns": "8", "endOffsets": "1563"}}]}]}