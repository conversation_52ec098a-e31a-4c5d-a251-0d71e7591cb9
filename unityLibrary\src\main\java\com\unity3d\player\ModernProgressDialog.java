package com.unity3d.player;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;

/**
 * Modern progress dialog using Material Design components
 * Replaces the deprecated ProgressDialog
 */
public class ModernProgressDialog {
    
    private android.app.AlertDialog dialog;
    private ProgressBar progressBar;
    private TextView titleText;
    private TextView messageText;
    private TextView progressText;
    private Button cancelButton;
    private OnCancelListener cancelListener;
    
    private Context context;
    private boolean isCancelable = true;
    private String title = "Downloading";
    private String message = "Please wait...";
    private int maxProgress = 100;
    private int currentProgress = 0;
    
    public interface OnCancelListener {
        void onCancel();
    }
    
    public ModernProgressDialog(Context context) {
        this.context = context;
        createDialog();
    }
    
    private void createDialog() {
        // Create custom layout programmatically since we don't have XML resources
        View dialogView = createDialogView();

        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(context)
                .setView(dialogView)
                .setCancelable(false); // We handle cancellation manually

        dialog = builder.create();
        
        // Set up cancel button
        if (cancelButton != null) {
            cancelButton.setOnClickListener(v -> {
                if (cancelListener != null) {
                    cancelListener.onCancel();
                }
                dismiss();
            });
        }
    }
    
    private View createDialogView() {
        // Create the layout programmatically
        android.widget.LinearLayout layout = new android.widget.LinearLayout(context);
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setPadding(48, 32, 48, 32);
        
        // Title
        titleText = new TextView(context);
        titleText.setText(title);
        titleText.setTextSize(18);
        titleText.setTextColor(0xFF000000);
        android.widget.LinearLayout.LayoutParams titleParams = 
            new android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT);
        titleParams.bottomMargin = 16;
        layout.addView(titleText, titleParams);
        
        // Message
        messageText = new TextView(context);
        messageText.setText(message);
        messageText.setTextSize(14);
        messageText.setTextColor(0xFF666666);
        android.widget.LinearLayout.LayoutParams messageParams = 
            new android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT);
        messageParams.bottomMargin = 24;
        layout.addView(messageText, messageParams);
        
        // Progress bar
        progressBar = new ProgressBar(context, null, android.R.attr.progressBarStyleHorizontal);
        progressBar.setMax(maxProgress);
        progressBar.setProgress(currentProgress);
        android.widget.LinearLayout.LayoutParams progressParams = 
            new android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT);
        progressParams.bottomMargin = 8;
        layout.addView(progressBar, progressParams);
        
        // Progress text
        progressText = new TextView(context);
        progressText.setText(currentProgress + "%");
        progressText.setTextSize(12);
        progressText.setTextColor(0xFF666666);
        progressText.setGravity(android.view.Gravity.CENTER);
        android.widget.LinearLayout.LayoutParams progressTextParams = 
            new android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT);
        progressTextParams.bottomMargin = 24;
        layout.addView(progressText, progressTextParams);
        
        // Cancel button
        if (isCancelable) {
            cancelButton = new Button(context);
            cancelButton.setText("Cancel");
            cancelButton.setBackgroundColor(0xFFE0E0E0);
            cancelButton.setTextColor(0xFF000000);
            android.widget.LinearLayout.LayoutParams buttonParams = 
                new android.widget.LinearLayout.LayoutParams(
                    android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                    android.widget.LinearLayout.LayoutParams.WRAP_CONTENT);
            buttonParams.gravity = android.view.Gravity.CENTER;
            layout.addView(cancelButton, buttonParams);
        }
        
        return layout;
    }
    
    public void setTitle(String title) {
        this.title = title;
        if (titleText != null) {
            titleText.setText(title);
        }
    }
    
    public void setMessage(String message) {
        this.message = message;
        if (messageText != null) {
            messageText.setText(message);
        }
    }
    
    public void setProgress(int progress) {
        this.currentProgress = Math.max(0, Math.min(progress, maxProgress));
        if (progressBar != null) {
            progressBar.setProgress(this.currentProgress);
        }
        if (progressText != null) {
            progressText.setText(this.currentProgress + "%");
        }
    }
    
    public void setMax(int max) {
        this.maxProgress = max;
        if (progressBar != null) {
            progressBar.setMax(max);
        }
    }
    
    public void setCancelable(boolean cancelable) {
        this.isCancelable = cancelable;
        if (cancelButton != null) {
            cancelButton.setVisibility(cancelable ? View.VISIBLE : View.GONE);
        }
    }
    
    public void setOnCancelListener(OnCancelListener listener) {
        this.cancelListener = listener;
    }
    
    public void show() {
        if (dialog != null && !dialog.isShowing()) {
            dialog.show();
        }
    }
    
    public void dismiss() {
        if (dialog != null && dialog.isShowing()) {
            dialog.dismiss();
        }
    }
    
    public boolean isShowing() {
        return dialog != null && dialog.isShowing();
    }
}
