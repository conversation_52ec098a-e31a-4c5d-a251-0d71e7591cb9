{"logs": [{"outputFile": "E:\\huoli-rk3566\\hl071302\\launcher\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-cs\\values-cs.xml", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\0afbca5d75ed49db5552569b9dce4707\\material-1.4.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,400,492,618,699,764,863,939,1000,1089,1156,1210,1278,1338,1392,1509,1569,1631,1685,1757,1879,1963,2055,2162,2240,2322,2410,2477,2543,2615,2692,2776,2848,2925,2999,3070,3158,3229,3322,3417,3491,3565,3661,3713,3780,3866,3954,4016,4080,4143,4253,4349,4448,4546", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,76,91,125,80,64,98,75,60,88,66,53,67,59,53,116,59,61,53,71,121,83,91,106,77,81,87,66,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,78", "endOffsets": "318,395,487,613,694,759,858,934,995,1084,1151,1205,1273,1333,1387,1504,1564,1626,1680,1752,1874,1958,2050,2157,2235,2317,2405,2472,2538,2610,2687,2771,2843,2920,2994,3065,3153,3224,3317,3412,3486,3560,3656,3708,3775,3861,3949,4011,4075,4138,4248,4344,4443,4541,4620"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3066,3143,3235,3361,3442,3507,3606,3682,3743,3832,3899,3953,4021,4081,4135,4252,4312,4374,4428,4500,4622,4706,4798,4905,4983,5065,5153,5220,5286,5358,5435,5519,5591,5668,5742,5813,5901,5972,6065,6160,6234,6308,6404,6456,6523,6609,6697,6759,6823,6886,6996,7092,7191,7289", "endLines": "7,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88", "endColumns": "12,76,91,125,80,64,98,75,60,88,66,53,67,59,53,116,59,61,53,71,121,83,91,106,77,81,87,66,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,78", "endOffsets": "368,3138,3230,3356,3437,3502,3601,3677,3738,3827,3894,3948,4016,4076,4130,4247,4307,4369,4423,4495,4617,4701,4793,4900,4978,5060,5148,5215,5281,5353,5430,5514,5586,5663,5737,5808,5896,5967,6060,6155,6229,6303,6399,6451,6518,6604,6692,6754,6818,6881,6991,7087,7186,7284,7363"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\f9db3af1d4b21c0f8eaed47c1558f861\\appcompat-1.2.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,509,614,731,809,885,976,1069,1164,1258,1352,1445,1540,1637,1728,1819,1903,2007,2119,2218,2324,2435,2537,2700,2798", "endColumns": "106,101,108,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,418,504,609,726,804,880,971,1064,1159,1253,1347,1440,1535,1632,1723,1814,1898,2002,2114,2213,2319,2430,2532,2695,2793,2876"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "373,480,582,691,777,882,999,1077,1153,1244,1337,1432,1526,1620,1713,1808,1905,1996,2087,2171,2275,2387,2486,2592,2703,2805,2968,7368", "endColumns": "106,101,108,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "475,577,686,772,877,994,1072,1148,1239,1332,1427,1521,1615,1708,1803,1900,1991,2082,2166,2270,2382,2481,2587,2698,2800,2963,3061,7446"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\07adfc08fe16d3453ab9291511ec05fe\\core-1.5.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "90", "startColumns": "4", "startOffsets": "7451", "endColumns": "100", "endOffsets": "7547"}}]}]}