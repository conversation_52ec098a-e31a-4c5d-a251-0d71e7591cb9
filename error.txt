FATAL EXCEPTION: main
                                                                                                    Process: com.cy.exercisetv, PID: 2501
                                                                                                    java.lang.RuntimeException: Unable to start activity ComponentInfo{com.cy.exercisetv/com.unity3d.player.UnityPlayerActivity}: java.lang.IllegalArgumentException: The style on this component requires your app theme to be Theme.AppCompat (or a descendant).
                                                                                                    	at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:3431)
                                                                                                    	at android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:3595)
                                                                                                    	at android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:85)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.executeCallbacks(TransactionExecutor.java:135)
                                                                                                    	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:95)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2066)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loop(Looper.java:223)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7664)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:592)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:947)
                                                                                                    Caused by: java.lang.IllegalArgumentException: The style on this component requires your app theme to be Theme.AppCompat (or a descendant).
                                                                                                    	at com.google.android.material.internal.ThemeEnforcement.checkTheme(ThemeEnforcement.java:243)
                                                                                                    	at com.google.android.material.internal.ThemeEnforcement.checkAppCompatTheme(ThemeEnforcement.java:213)
                                                                                                    	at com.google.android.material.internal.ThemeEnforcement.checkCompatibleTheme(ThemeEnforcement.java:148)
                                                                                                    	at com.google.android.material.internal.ThemeEnforcement.obtainStyledAttributes(ThemeEnforcement.java:76)
                                                                                                    	at com.google.android.material.dialog.MaterialDialogs.getDialogBackgroundInsets(MaterialDialogs.java:60)
                                                                                                    	at com.google.android.material.dialog.MaterialAlertDialogBuilder.<init>(MaterialAlertDialogBuilder.java:117)
                                                                                                    	at com.google.android.material.dialog.MaterialAlertDialogBuilder.<init>(MaterialAlertDialogBuilder.java:103)
                                                                                                    	at com.unity3d.player.ModernUpdateManager.showUpdateDialog(ModernUpdateManager.java:119)
                                                                                                    	at com.unity3d.player.ModernUpdateManager.checkUpdate(ModernUpdateManager.java:115)
                                                                                                    	at com.unity3d.player.UnityPlayerActivity.checkForUpdates(UnityPlayerActivity.java:177)
                                                                                                    	at com.unity3d.player.UnityPlayerActivity.testUp(UnityPlayerActivity.java:160)
                                                                                                    	at com.unity3d.player.UnityPlayerActivity.onCreate(UnityPlayerActivity.java:106)
                                                                                                    	at android.app.Activity.performCreate(Activity.java:8022)
                                                                                                    	at android.app.Activity.performCreate(Activity.java:8006)
                                                                                                    	at android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1309)
                                                                                                    	at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:3404)