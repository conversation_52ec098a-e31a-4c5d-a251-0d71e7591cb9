{"logs": [{"outputFile": "E:\\huoli-rk3566\\hl071302\\launcher\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values\\values.xml", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\083e340af32f320e6ad3917bcf6db07c\\work-runtime-2.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}}, {"source": "E:\\huoli-rk3566\\hl071302\\launcher\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "5,2,7", "startColumns": "0,0,0", "startOffsets": "190,53,288", "endLines": "6,4,10", "endColumns": "8,8,8", "endOffsets": "286,188,513"}, "to": {"startLines": "87,100,103", "startColumns": "4,4,4", "startOffsets": "5236,6094,6232", "endLines": "88,102,106", "endColumns": "8,8,8", "endOffsets": "5331,6227,6454"}}, {"source": "E:\\huoli-rk3566\\hl071302\\launcher\\src\\main\\res\\values\\ids.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "46", "endOffsets": "97"}, "to": {"startLines": "80", "startColumns": "4", "startOffsets": "4798", "endColumns": "45", "endOffsets": "4839"}}, {"source": "E:\\huoli-rk3566\\hl071302\\launcher\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,3", "startColumns": "2,2", "startOffsets": "55,104", "endColumns": "47,65", "endOffsets": "100,167"}, "to": {"startLines": "84,85", "startColumns": "4,4", "startOffsets": "5047,5097", "endColumns": "49,67", "endOffsets": "5092,5160"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\f6de2a670d04d6e6d777da00f7299d4c\\lifecycle-viewmodel-2.3.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "81", "startColumns": "4", "startOffsets": "4844", "endColumns": "49", "endOffsets": "4889"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\faae5e4478c326f3f4fa86a104d30d9e\\core-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,83,84,88,89,90,91,98,141,173,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,187,245,319,389,457,529,599,660,734,807,868,929,991,1055,1117,1178,1246,1346,1406,1472,1545,1614,1671,1723,1785,1857,1933,1998,2057,2116,2176,2236,2296,2356,2416,2476,2536,2596,2656,2716,2775,2835,2895,2955,3015,3075,3135,3195,3255,3315,3375,3434,3494,3554,3613,3672,3731,3790,3849,3908,3943,3978,4033,4096,4151,4209,4266,4316,4377,4434,4468,4503,4538,4608,4679,4796,4997,5107,5308,5437,5509,5576,5874,8780,10845,12605", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,82,83,87,88,89,90,97,140,172,209,216", "endColumns": "68,62,57,73,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,56,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24", "endOffsets": "119,182,240,314,384,452,524,594,655,729,802,863,924,986,1050,1112,1173,1241,1341,1401,1467,1540,1609,1666,1718,1780,1852,1928,1993,2052,2111,2171,2231,2291,2351,2411,2471,2531,2591,2651,2711,2770,2830,2890,2950,3010,3070,3130,3190,3250,3310,3370,3429,3489,3549,3608,3667,3726,3785,3844,3903,3938,3973,4028,4091,4146,4204,4261,4311,4372,4429,4463,4498,4533,4603,4674,4791,4992,5102,5303,5432,5504,5571,5869,8775,10840,12600,12977"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,82,86,89,90,94,95,99,107,108,109,116,159,191,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,384,447,505,579,649,717,789,859,920,994,1067,1128,1189,1251,1315,1377,1438,1506,1606,1666,1732,1805,1874,1931,1983,2045,2117,2193,2258,2317,2376,2436,2496,2556,2616,2676,2736,2796,2856,2916,2976,3035,3095,3155,3215,3275,3335,3395,3455,3515,3575,3635,3694,3754,3814,3873,3932,3991,4050,4109,4168,4203,4238,4293,4356,4411,4469,4526,4576,4637,4694,4728,4763,4894,5165,5336,5453,5654,5764,5965,6459,6531,6598,6896,9802,11867,13627", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,82,86,89,93,94,98,99,107,108,115,158,190,227,234", "endColumns": "68,62,57,73,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,56,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24", "endOffsets": "379,442,500,574,644,712,784,854,915,989,1062,1123,1184,1246,1310,1372,1433,1501,1601,1661,1727,1800,1869,1926,1978,2040,2112,2188,2253,2312,2371,2431,2491,2551,2611,2671,2731,2791,2851,2911,2971,3030,3090,3150,3210,3270,3330,3390,3450,3510,3570,3630,3689,3749,3809,3868,3927,3986,4045,4104,4163,4198,4233,4288,4351,4406,4464,4521,4571,4632,4689,4723,4758,4793,4959,5231,5448,5649,5759,5960,6089,6526,6593,6891,9797,11862,13622,13999"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\d94a4bb1a6fc6cbf701df443dbd8cef9\\jetified-startup-runtime-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "83", "startColumns": "4", "startOffsets": "4964", "endColumns": "82", "endOffsets": "5042"}}]}]}