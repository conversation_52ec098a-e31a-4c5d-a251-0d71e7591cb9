{"logs": [{"outputFile": "E:\\huoli-rk3566\\hl071302\\launcher\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-hr\\values-hr.xml", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\f9db3af1d4b21c0f8eaed47c1558f861\\appcompat-1.2.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,430,525,632,718,822,941,1026,1108,1199,1292,1387,1481,1581,1674,1769,1864,1955,2046,2132,2236,2348,2449,2554,2668,2770,2939,7429", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "425,520,627,713,817,936,1021,1103,1194,1287,1382,1476,1576,1669,1764,1859,1950,2041,2127,2231,2343,2444,2549,2663,2765,2934,3031,7509"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\07adfc08fe16d3453ab9291511ec05fe\\core-1.5.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "89", "startColumns": "4", "startOffsets": "7514", "endColumns": "100", "endOffsets": "7610"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\0afbca5d75ed49db5552569b9dce4707\\material-1.4.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,275,360,456,586,670,738,834,902,965,1073,1139,1195,1266,1326,1380,1506,1563,1625,1679,1754,1888,1973,2054,2161,2245,2331,2422,2489,2555,2629,2707,2795,2867,2944,3024,3098,3191,3264,3356,3452,3526,3602,3698,3750,3817,3904,3991,4053,4117,4180,4286,4387,4484,4588", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,84,95,129,83,67,95,67,62,107,65,55,70,59,53,125,56,61,53,74,133,84,80,106,83,85,90,66,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,79", "endOffsets": "270,355,451,581,665,733,829,897,960,1068,1134,1190,1261,1321,1375,1501,1558,1620,1674,1749,1883,1968,2049,2156,2240,2326,2417,2484,2550,2624,2702,2790,2862,2939,3019,3093,3186,3259,3351,3447,3521,3597,3693,3745,3812,3899,3986,4048,4112,4175,4281,4382,4479,4583,4663"}, "to": {"startLines": "2,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3036,3121,3217,3347,3431,3499,3595,3663,3726,3834,3900,3956,4027,4087,4141,4267,4324,4386,4440,4515,4649,4734,4815,4922,5006,5092,5183,5250,5316,5390,5468,5556,5628,5705,5785,5859,5952,6025,6117,6213,6287,6363,6459,6511,6578,6665,6752,6814,6878,6941,7047,7148,7245,7349", "endLines": "6,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87", "endColumns": "12,84,95,129,83,67,95,67,62,107,65,55,70,59,53,125,56,61,53,74,133,84,80,106,83,85,90,66,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,79", "endOffsets": "320,3116,3212,3342,3426,3494,3590,3658,3721,3829,3895,3951,4022,4082,4136,4262,4319,4381,4435,4510,4644,4729,4810,4917,5001,5087,5178,5245,5311,5385,5463,5551,5623,5700,5780,5854,5947,6020,6112,6208,6282,6358,6454,6506,6573,6660,6747,6809,6873,6936,7042,7143,7240,7344,7424"}}]}]}