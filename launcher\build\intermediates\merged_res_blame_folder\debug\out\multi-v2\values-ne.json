{"logs": [{"outputFile": "E:\\huoli-rk3566\\hl071302\\launcher\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-ne\\values-ne.xml", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\0afbca5d75ed49db5552569b9dce4707\\material-1.4.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,299,401,511,598,664,760,826,887,992,1064,1122,1196,1258,1312,1425,1485,1546,1605,1683,1807,1888,1973,2079,2160,2243,2326,2393,2459,2536,2615,2703,2772,2848,2929,2997,3088,3166,3259,3356,3430,3509,3607,3667,3733,3821,3909,3971,4039,4102,4207,4325,4420,4540", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,101,109,86,65,95,65,60,104,71,57,73,61,53,112,59,60,58,77,123,80,84,105,80,82,82,66,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,83", "endOffsets": "212,294,396,506,593,659,755,821,882,987,1059,1117,1191,1253,1307,1420,1480,1541,1600,1678,1802,1883,1968,2074,2155,2238,2321,2388,2454,2531,2610,2698,2767,2843,2924,2992,3083,3161,3254,3351,3425,3504,3602,3662,3728,3816,3904,3966,4034,4097,4202,4320,4415,4535,4619"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3050,3132,3234,3344,3431,3497,3593,3659,3720,3825,3897,3955,4029,4091,4145,4258,4318,4379,4438,4516,4640,4721,4806,4912,4993,5076,5159,5226,5292,5369,5448,5536,5605,5681,5762,5830,5921,5999,6092,6189,6263,6342,6440,6500,6566,6654,6742,6804,6872,6935,7040,7158,7253,7373", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,81,101,109,86,65,95,65,60,104,71,57,73,61,53,112,59,60,58,77,123,80,84,105,80,82,82,66,65,76,78,87,68,75,80,67,90,77,92,96,73,78,97,59,65,87,87,61,67,62,104,117,94,119,83", "endOffsets": "262,3127,3229,3339,3426,3492,3588,3654,3715,3820,3892,3950,4024,4086,4140,4253,4313,4374,4433,4511,4635,4716,4801,4907,4988,5071,5154,5221,5287,5364,5443,5531,5600,5676,5757,5825,5916,5994,6087,6184,6258,6337,6435,6495,6561,6649,6737,6799,6867,6930,7035,7153,7248,7368,7452"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\07adfc08fe16d3453ab9291511ec05fe\\core-1.5.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7537", "endColumns": "100", "endOffsets": "7633"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\f9db3af1d4b21c0f8eaed47c1558f861\\appcompat-1.2.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,327,435,526,633,760,844,923,1014,1107,1202,1296,1396,1489,1584,1678,1769,1860,1946,2059,2160,2256,2369,2479,2603,2777,2888", "endColumns": "110,110,107,90,106,126,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,123,173,110,79", "endOffsets": "211,322,430,521,628,755,839,918,1009,1102,1197,1291,1391,1484,1579,1673,1764,1855,1941,2054,2155,2251,2364,2474,2598,2772,2883,2963"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "267,378,489,597,688,795,922,1006,1085,1176,1269,1364,1458,1558,1651,1746,1840,1931,2022,2108,2221,2322,2418,2531,2641,2765,2939,7457", "endColumns": "110,110,107,90,106,126,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,123,173,110,79", "endOffsets": "373,484,592,683,790,917,1001,1080,1171,1264,1359,1453,1553,1646,1741,1835,1926,2017,2103,2216,2317,2413,2526,2636,2760,2934,3045,7532"}}]}]}