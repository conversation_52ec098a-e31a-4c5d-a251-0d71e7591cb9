{"logs": [{"outputFile": "E:\\huoli-rk3566\\hl071302\\launcher\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-ja\\values-ja.xml", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\0afbca5d75ed49db5552569b9dce4707\\material-1.4.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,208,277,362,466,542,605,689,753,811,892,956,1011,1070,1127,1181,1274,1330,1387,1441,1507,1607,1683,1764,1856,1918,1980,2059,2126,2192,2262,2332,2409,2473,2544,2612,2675,2754,2817,2897,2979,3051,3122,3194,3242,3306,3381,3458,3520,3584,3647,3733,3817,3898,3983", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,68,84,103,75,62,83,63,57,80,63,54,58,56,53,92,55,56,53,65,99,75,80,91,61,61,78,66,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,72", "endOffsets": "203,272,357,461,537,600,684,748,806,887,951,1006,1065,1122,1176,1269,1325,1382,1436,1502,1602,1678,1759,1851,1913,1975,2054,2121,2187,2257,2327,2404,2468,2539,2607,2670,2749,2812,2892,2974,3046,3117,3189,3237,3301,3376,3453,3515,3579,3642,3728,3812,3893,3978,4051"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2844,2913,2998,3102,3178,3241,3325,3389,3447,3528,3592,3647,3706,3763,3817,3910,3966,4023,4077,4143,4243,4319,4400,4492,4554,4616,4695,4762,4828,4898,4968,5045,5109,5180,5248,5311,5390,5453,5533,5615,5687,5758,5830,5878,5942,6017,6094,6156,6220,6283,6369,6453,6534,6619", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,68,84,103,75,62,83,63,57,80,63,54,58,56,53,92,55,56,53,65,99,75,80,91,61,61,78,66,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,72", "endOffsets": "253,2908,2993,3097,3173,3236,3320,3384,3442,3523,3587,3642,3701,3758,3812,3905,3961,4018,4072,4138,4238,4314,4395,4487,4549,4611,4690,4757,4823,4893,4963,5040,5104,5175,5243,5306,5385,5448,5528,5610,5682,5753,5825,5873,5937,6012,6089,6151,6215,6278,6364,6448,6529,6614,6687"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\f9db3af1d4b21c0f8eaed47c1558f861\\appcompat-1.2.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "258,355,448,553,635,733,841,919,994,1085,1178,1273,1367,1467,1560,1655,1749,1840,1931,2009,2111,2209,2304,2407,2503,2599,2747,6692", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "350,443,548,630,728,836,914,989,1080,1173,1268,1362,1462,1555,1650,1744,1835,1926,2004,2106,2204,2299,2402,2498,2594,2742,2839,6766"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\07adfc08fe16d3453ab9291511ec05fe\\core-1.5.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "6771", "endColumns": "100", "endOffsets": "6867"}}]}]}