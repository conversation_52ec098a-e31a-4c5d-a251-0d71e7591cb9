{"logs": [{"outputFile": "E:\\huoli-rk3566\\hl071302\\launcher\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-or\\values-or.xml", "map": [{"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\07adfc08fe16d3453ab9291511ec05fe\\core-1.5.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "88", "startColumns": "4", "startOffsets": "7334", "endColumns": "100", "endOffsets": "7430"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\0afbca5d75ed49db5552569b9dce4707\\material-1.4.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,398,503,582,647,736,801,860,946,1010,1073,1146,1210,1264,1376,1434,1496,1550,1622,1744,1831,1917,2027,2104,2185,2276,2343,2409,2479,2556,2643,2714,2791,2860,2929,3020,3092,3181,3270,3344,3416,3502,3552,3618,3698,3782,3844,3908,3971,4071,4168,4260,4359", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,93,104,78,64,88,64,58,85,63,62,72,63,53,111,57,61,53,71,121,86,85,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,83", "endOffsets": "222,299,393,498,577,642,731,796,855,941,1005,1068,1141,1205,1259,1371,1429,1491,1545,1617,1739,1826,1912,2022,2099,2180,2271,2338,2404,2474,2551,2638,2709,2786,2855,2924,3015,3087,3176,3265,3339,3411,3497,3547,3613,3693,3777,3839,3903,3966,4066,4163,4255,4354,4438"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3028,3105,3199,3304,3383,3448,3537,3602,3661,3747,3811,3874,3947,4011,4065,4177,4235,4297,4351,4423,4545,4632,4718,4828,4905,4986,5077,5144,5210,5280,5357,5444,5515,5592,5661,5730,5821,5893,5982,6071,6145,6217,6303,6353,6419,6499,6583,6645,6709,6772,6872,6969,7061,7160", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86", "endColumns": "12,76,93,104,78,64,88,64,58,85,63,62,72,63,53,111,57,61,53,71,121,86,85,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,83", "endOffsets": "272,3100,3194,3299,3378,3443,3532,3597,3656,3742,3806,3869,3942,4006,4060,4172,4230,4292,4346,4418,4540,4627,4713,4823,4900,4981,5072,5139,5205,5275,5352,5439,5510,5587,5656,5725,5816,5888,5977,6066,6140,6212,6298,6348,6414,6494,6578,6640,6704,6767,6867,6964,7056,7155,7239"}}, {"source": "E:\\gradle\\gradle-6.7.1\\caches\\transforms-2\\files-2.1\\f9db3af1d4b21c0f8eaed47c1558f861\\appcompat-1.2.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,829,905,996,1089,1185,1280,1380,1473,1568,1664,1755,1845,1934,2044,2148,2254,2365,2469,2587,2750,2856", "endColumns": "118,109,106,85,103,119,77,75,90,92,95,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,824,900,991,1084,1180,1275,1375,1468,1563,1659,1750,1840,1929,2039,2143,2249,2360,2464,2582,2745,2851,2941"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,396,506,613,699,803,923,1001,1077,1168,1261,1357,1452,1552,1645,1740,1836,1927,2017,2106,2216,2320,2426,2537,2641,2759,2922,7244", "endColumns": "118,109,106,85,103,119,77,75,90,92,95,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "391,501,608,694,798,918,996,1072,1163,1256,1352,1447,1547,1640,1735,1831,1922,2012,2101,2211,2315,2421,2532,2636,2754,2917,3023,7329"}}]}]}