package com.unity3d.player;

import android.app.Activity;
import android.os.Bundle;
import android.util.Log;

/**
 * Example showing how to use the ModernUpdateManager
 * This replaces the old UpdateManager usage
 */
public class UpdateManagerExample extends Activity {
    
    private static final String TAG = "UpdateExample";
    private ModernUpdateManager updateManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Initialize the modern update manager
        updateManager = new ModernUpdateManager(this);
        
        // Set download URL
        updateManager.setDownloadUrl("https://example.com/app-update.apk");
        
        // Set up callback to handle update events
        updateManager.setUpdateCallback(new ModernUpdateManager.UpdateCallback() {
            @Override
            public void onUpdateStarted() {
                Log.d(TAG, "Update download started");
                // You can show additional UI feedback here
            }
            
            @Override
            public void onProgressUpdate(int progress) {
                Log.d(TAG, "Download progress: " + progress + "%");
                // Update your custom progress indicators if needed
            }
            
            @Override
            public void onUpdateCompleted(String filePath) {
                Log.d(TAG, "Update completed. File saved at: " + filePath);
                // Handle successful download completion
                // The APK installation will be triggered automatically
            }
            
            @Override
            public void onUpdateFailed(String error) {
                Log.e(TAG, "Update failed: " + error);
                // Handle download failure
                // You might want to show an error dialog or retry option
            }
            
            @Override
            public void onUpdateCancelled() {
                Log.d(TAG, "Update cancelled by user");
                // Handle user cancellation
            }
        });
        
        // Enable retry mechanism (optional, enabled by default)
        updateManager.setRetryEnabled(true);
        
        // Example: Check for updates when activity starts
        checkForUpdates();
    }
    
    private void checkForUpdates() {
        // Check network status first
        if (!updateManager.isNetworkAvailable()) {
            Log.w(TAG, "No network available. Network type: " + updateManager.getNetworkType());
            return;
        }
        
        Log.d(TAG, "Network available. Type: " + updateManager.getNetworkType());
        
        // Start update check
        // Set isForced = false to allow user to cancel
        // Set isForced = true to force update (no cancel option)
        updateManager.checkUpdate(false);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // Important: Clean up resources to prevent memory leaks
        if (updateManager != null) {
            updateManager.cleanup();
        }
    }
    
    // Example method showing how to trigger update programmatically
    public void triggerManualUpdate() {
        if (updateManager != null) {
            updateManager.checkUpdate(true); // Force update
        }
    }
}

///**
// * MIGRATION GUIDE FROM OLD UpdateManager:
// *
// * OLD CODE:
// * --------
// * UpdateManager updateManager = new UpdateManager(context);
// * updateManager.setDownloadUrl("https://example.com/app.apk");
// * updateManager.checkUpdate(false);
// *
// * NEW CODE (UNITY COMPATIBLE):
// * ---------------------------
// * ModernUpdateManager updateManager = new ModernUpdateManager(context);
// * updateManager.setDownloadUrl("https://example.com/app.apk");
// * updateManager.setUpdateCallback(new ModernUpdateManager.UpdateCallback() {
// *     @Override
// *     public void onUpdateStarted() { /* Handle start */ }
// *
// *     @Override
// *     public void onProgressUpdate(int progress) { /* Handle progress */ }
// *
// *     @Override
// *     public void onUpdateCompleted(String filePath) { /* Handle completion */ }
// *
// *     @Override
// *     public void onUpdateFailed(String error) { /* Handle failure */ }
// *
// *     @Override
// *     public void onUpdateCancelled() { /* Handle cancellation */ }
// * });
// * updateManager.checkUpdate(false);
// *
// * // Don't forget to cleanup!
// * updateManager.cleanup(); // Call this in onDestroy()
// *
// *
// * THEME COMPATIBILITY ISSUE FIXED:
// * --------------------------------
// * The original error was caused by MaterialAlertDialogBuilder requiring
// * Theme.AppCompat, but Unity uses its own theme system.
// *
// * SOLUTION: We replaced MaterialAlertDialogBuilder with standard AlertDialog.Builder
// * to ensure compatibility with Unity's theme system.
// *
// *
// * KEY IMPROVEMENTS:
// * ----------------
// * 1. No more memory leaks - Uses WorkManager instead of raw threads
// * 2. Better error handling - Automatic retry with exponential backoff
// * 3. Unity compatible UI - Standard AlertDialog instead of Material Design
// * 4. Network monitoring - Handles network changes gracefully
// * 5. Proper lifecycle management - Cleanup methods to prevent leaks
// * 6. Better progress tracking - Real-time progress updates
// * 7. Cancellation support - Users can cancel downloads safely
// * 8. Background processing - Downloads continue even if app is backgrounded
// */