package com.unity3d.player;

import android.app.Activity;
import android.os.Bundle;
import android.util.Log;

/**
 * Example showing how to use the ModernUpdateManager
 * This replaces the old UpdateManager usage
 */
public class UpdateManagerExample extends Activity {
    
    private static final String TAG = "UpdateExample";
    private ModernUpdateManager updateManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Initialize the modern update manager
        updateManager = new ModernUpdateManager(this);
        
        // Set download URL
        updateManager.setDownloadUrl("https://example.com/app-update.apk");
        
        // Set up callback to handle update events
        updateManager.setUpdateCallback(new ModernUpdateManager.UpdateCallback() {
            @Override
            public void onUpdateStarted() {
                Log.d(TAG, "Update download started");
                // You can show additional UI feedback here
            }
            
            @Override
            public void onProgressUpdate(int progress) {
                Log.d(TAG, "Download progress: " + progress + "%");
                // Update your custom progress indicators if needed
            }
            
            @Override
            public void onUpdateCompleted(String filePath) {
                Log.d(TAG, "Update completed. File saved at: " + filePath);
                // Handle successful download completion
                // The APK installation will be triggered automatically
            }
            
            @Override
            public void onUpdateFailed(String error) {
                Log.e(TAG, "Update failed: " + error);
                // Handle download failure
                // You might want to show an error dialog or retry option
            }
            
            @Override
            public void onUpdateCancelled() {
                Log.d(TAG, "Update cancelled by user");
                // Handle user cancellation
            }
        });
        
        // Enable retry mechanism (optional, enabled by default)
        updateManager.setRetryEnabled(true);
        
        // Example: Check for updates when activity starts
        checkForUpdates();
    }
    
    private void checkForUpdates() {
        // Check network status first
        if (!updateManager.isNetworkAvailable()) {
            Log.w(TAG, "No network available. Network type: " + updateManager.getNetworkType());
            return;
        }
        
        Log.d(TAG, "Network available. Type: " + updateManager.getNetworkType());
        
        // Start update check
        // Set isForced = false to allow user to cancel
        // Set isForced = true to force update (no cancel option)
        updateManager.checkUpdate(false);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // Important: Clean up resources to prevent memory leaks
        if (updateManager != null) {
            updateManager.cleanup();
        }
    }
    
    // Example method showing how to trigger update programmatically
    public void triggerManualUpdate() {
        if (updateManager != null) {
            updateManager.checkUpdate(true); // Force update
        }
    }
}

