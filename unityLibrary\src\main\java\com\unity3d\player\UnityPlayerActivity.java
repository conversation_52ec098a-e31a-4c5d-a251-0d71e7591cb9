// GENERATED BY UNITY. REMOVE THIS COMMENT TO PREVENT OVERWRITING WHEN EXPORTING AGAIN
package com.unity3d.player;

import android.Manifest;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.NetworkRequest;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.net.wifi.WifiNetworkSpecifier;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.Window;
import android.os.Process;
import android.widget.Toast;

import com.cykj.cymobile.ModelUtils;
import com.cykj.cymobile.PoseDetector;
import com.cykj.cymobile.PoseResult;
import com.google.gson.Gson;
import com.umeng.commonsdk.UMConfigure;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

public class UnityPlayerActivity extends Activity implements IUnityPlayerLifecycleEvents, NetRequest.onRequestListener {
    protected UnityPlayer mUnityPlayer; // don't change the name of this variable; referenced from native code
    private static UnityPlayerActivity unityPlayerActivity;
    private static PoseDetector poseDetector;
    private static String TAG = "UnityPlayerActivity===";
    private static final int WIFI_PANEL_REQUEST_CODE = 100;
    private static final int MY_PERMISSIONS_REQUEST_DOWN = 1;
    private static final int PERMISSIONS_REQUEST_LOCATE = 2;
    private static NetRequest requestUtil;
    private static WifiManager wifiManager;
    private static List<ScanResult> wifiList;
    private static BroadcastReceiver wifiScanReceiver;
    private static String defaultWifiJson;
    private static boolean isReceiverRegistered = false;
    private ModernUpdateManager updateManager;

    // Override this in your custo
    // m UnityPlayerActivity to tweak the command line arguments passed to the Unity Android Player
    // The command line arguments are passed as a string, separated by spaces
    // UnityPlayerActivity calls this from 'onCreate'
    // Supported: -force-gles20, -force-gles30, -force-gles31, -force-gles31aep, -force-gles32, -force-gles, -force-vulkan
    // See https://docs.unity3d.com/Manual/CommandLineArguments.html
    // @param cmdLine the current command line arguments, may be null
    // @return the modified command line string or null
    protected String updateUnityCommandLineArguments(String cmdLine) {
        return cmdLine;
    }

    // Setup activity layout
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        super.onCreate(savedInstanceState);

        String cmdLine = updateUnityCommandLineArguments(getIntent().getStringExtra("unity"));
        getIntent().putExtra("unity", cmdLine);

        mUnityPlayer = new UnityPlayer(this, this);
        setContentView(mUnityPlayer);
        mUnityPlayer.requestFocus();
        wifiManager = (WifiManager) getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        IntentFilter filter = new IntentFilter(Intent.ACTION_CLOSE_SYSTEM_DIALOGS);
        registerReceiver(mHomeKeyReceiver, filter);
        unityPlayerActivity = UnityPlayerActivity.this;
        poseDetector = new PoseDetector();
        // 从Assets复制模型到缓存目录
        String modelPath = ModelUtils.copyAssetToCache(this, "yolov8n-pose.rknn");
        if (modelPath == null) {
            Toast.makeText(this, "Model file copy failed", Toast.LENGTH_SHORT).show();
//            return;
        }
        // 初始化模型
        int ret = poseDetector.init(modelPath);
        if (ret != 0) {
            Log.e(TAG, "Failed to initialize model: " + ret);
            Toast.makeText(this, "Model initialization failed", Toast.LENGTH_SHORT).show();
//            return;
        }
//        initNet();
        initData();
        testUp();
    }

    private void testUp() {
        String s = "{\"error\":0,\"msg\":\"success\",\"data\":{\"version\":\"1.3.3\",\"version_explain\":\"1.修复bug，优化体验。\",\"if_forced_update\":1,\"version_status\":1,\"visitor_status\":0,\"apk_url\":\"https://res.eaglecreative.top/apk/ARExerciseTV/test.apk\"}}";
        Gson gson = new Gson();
        VersionInfo versionInfo = gson.fromJson(s, VersionInfo.class);
        if (versionInfo.getError() == 0) {
            String version = versionInfo.getData().getVersion();
            if (!version.equals(APKVersionCodeUtils.getVerName(unityPlayerActivity)) && versionInfo.getData().getVersion_status() == 1) {
                boolean isForce = versionInfo.getData().getIf_forced_update() == 1 ? true : false;
                updateManager = new ModernUpdateManager(this);

                // Set download URL
                updateManager.setDownloadUrl(versionInfo.getData().getApk_url());

                // Set up callback to handle update events
                updateManager.setUpdateCallback(new ModernUpdateManager.UpdateCallback() {
                    @Override
                    public void onUpdateStarted() {
                        Log.d(TAG, "Update download started");
                        // You can show additional UI feedback here
                    }

                    @Override
                    public void onProgressUpdate(int progress) {
                        Log.d(TAG, "Download progress: " + progress + "%");
                        // Update your custom progress indicators if needed
                    }

                    @Override
                    public void onUpdateCompleted(String filePath) {
                        Log.d(TAG, "Update completed. File saved at: " + filePath);
                        // Handle successful download completion
                        // The APK installation will be triggered automatically
                    }

                    @Override
                    public void onUpdateFailed(String error) {
                        Log.e(TAG, "Update failed: " + error);
                        // Handle download failure
                        // You might want to show an error dialog or retry option
                    }

                    @Override
                    public void onUpdateCancelled() {
                        Log.d(TAG, "Update cancelled by user");
                        // Handle user cancellation
                    }
                });

                // Enable retry mechanism (optional, enabled by default)
                updateManager.setRetryEnabled(true);

                // Example: Check for updates when activity starts
                checkForUpdates(isForce);
            }
        }
    }

    private void checkForUpdates(boolean isForce) {
        // Check network status first
        if (!updateManager.isNetworkAvailable()) {
            Log.w(TAG, "No network available. Network type: " + updateManager.getNetworkType());
            return;
        }

        Log.d(TAG, "Network available. Type: " + updateManager.getNetworkType());

        // Start update check
        // Set isForced = false to allow user to cancel
        // Set isForced = true to force update (no cancel option)
        updateManager.checkUpdate(isForce);
    }

    private void initData() {
        if (ContextCompat.checkSelfPermission(unityPlayerActivity, Manifest.permission.ACCESS_FINE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(
                    unityPlayerActivity,
                    new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                    PERMISSIONS_REQUEST_LOCATE
            );
        } else {
            getWifiInfoList(false);
        }
    }

    private void initNet() {
        if (NetWorkUtils.getAPNType(UnityPlayerActivity.this) == 0) {
            return;
        }
        requestUtil = new NetRequest(this);
        requestUtil.setNetworkRequest(this);
        requestUtil.updateVersion();
    }

    public static void getWifiInfoList(boolean needJson) {
        if (wifiManager == null) {
            wifiManager = (WifiManager) unityPlayerActivity.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        }
        // 注册前先注销旧接收器（如果存在）
        if (isReceiverRegistered && wifiScanReceiver != null) {
            try {
                unityPlayerActivity.unregisterReceiver(wifiScanReceiver);
            } catch (Exception e) { /* 忽略 */ }
        }
        // 注册广播接收器
        wifiScanReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                boolean success = intent.getBooleanExtra(WifiManager.EXTRA_RESULTS_UPDATED, false);
                if (success) {
                    scanSuccess(needJson);
                } else {
                    scanFailure(needJson);
                }
            }
        };
        // 注册广播
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION);
        unityPlayerActivity.registerReceiver(wifiScanReceiver, intentFilter);
        isReceiverRegistered = true; // 标记已注册
        // 开始扫描
        boolean success = wifiManager.startScan();
        if (!success) {
            scanFailure(needJson);
        }
    }

    private static void scanSuccess(boolean needJson) {
        wifiList = wifiManager.getScanResults();
        if (needJson) {
            if (wifiList == null || wifiList.isEmpty() || wifiList.size() == 0) {
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiListReceived", "0");
                return;
            }
        }
        StringBuilder wifiInfo = new StringBuilder();
        List<WifiInfo> wifiInfoList = new ArrayList<>();
        for (ScanResult scanResult : wifiList) {
            wifiInfo.append("SSID: ").append(scanResult.SSID)
                    .append(", 信号强度: ").append(scanResult.level)
                    .append(", BSSID: ").append(scanResult.BSSID)
                    .append("\n");
            WifiInfo info = new WifiInfo();
            info.setSSID(scanResult.SSID);
            info.setBSSID(scanResult.BSSID);
            info.setLevel(scanResult.level);
            wifiInfoList.add(info);
        }
        //wifiInfoList转Json
        Gson gson = new Gson();
        defaultWifiJson = gson.toJson(wifiInfoList);
        Log.i(TAG, defaultWifiJson);
        if (needJson) {
            UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiListReceived", defaultWifiJson);
        }
        // 注销接收器
        if (isReceiverRegistered && wifiScanReceiver != null) {
            try {
                unityPlayerActivity.unregisterReceiver(wifiScanReceiver);
                isReceiverRegistered = false;
            } catch (Exception e) { /* 处理异常 */ }
        }
    }

    private static void scanFailure(boolean needJson) {
        // 如果扫描失败，尝试从最后一次的扫描结果获取
        wifiList = wifiManager.getScanResults();
        if (needJson) {
            if (wifiList == null || wifiList.isEmpty() || wifiList.size() == 0) {
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiListReceived", "0");
                return;
            }
        }
        StringBuilder wifiInfo = new StringBuilder();
        List<WifiInfo> wifiInfoList = new ArrayList<>();
        if (wifiList != null && !wifiList.isEmpty()) {
            for (ScanResult scanResult : wifiList) {
                wifiInfo.append("SSID: ").append(scanResult.SSID)
                        .append(", 信号强度: ").append(scanResult.level)
                        .append(", BSSID: ").append(scanResult.BSSID)
                        .append("\n");
                WifiInfo info = new WifiInfo();
                info.setSSID(scanResult.SSID);
                info.setBSSID(scanResult.BSSID);
                info.setLevel(scanResult.level);
                wifiInfoList.add(info);
            }
        } else {
            if (needJson) {
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiListReceived", "0");
            }
            return;
        }
        Gson gson = new Gson();
        defaultWifiJson = gson.toJson(wifiInfoList);
        Log.i(TAG, defaultWifiJson);
        if (needJson) {
            UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiListReceived", defaultWifiJson);
        }
        // 取消注册广播接收器
        if (null != wifiScanReceiver) {
            try {
                unityPlayerActivity.unregisterReceiver(wifiScanReceiver);
            } catch (IllegalArgumentException e) {
                // 接收器未注册时忽略异常
                Log.w(TAG, "Receiver already unregistered", e);
            }
            wifiScanReceiver = null;
        }
    }

    private void cleanuplmageResources() {

    }

    /**
     * 获取当前连接wifi信息
     */
    private static void getCurrentConnectedWifi() {
        unityPlayerActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                WifiInfo currentWifiInfo = new WifiInfo();
                if (wifiManager == null) {
                    wifiManager = (WifiManager) unityPlayerActivity.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
                }
                android.net.wifi.WifiInfo connectionInfo = wifiManager.getConnectionInfo();
                if (connectionInfo != null && connectionInfo.getNetworkId() != -1) {
                    // Android 10(Q)及以上版本，SSID可能返回<unknown ssid>
                    String ssid = connectionInfo.getSSID();
                    if (ssid.startsWith("\"") && ssid.endsWith("\"")) {
                        ssid = ssid.substring(1, ssid.length() - 1);
                    }
                    currentWifiInfo.setSSID(ssid);
                    currentWifiInfo.setBSSID(connectionInfo.getBSSID());
                    currentWifiInfo.setLevel(WifiManager.calculateSignalLevel(connectionInfo.getRssi(), 5));
                    Gson gson = new Gson();
                    String wifiJson = gson.toJson(currentWifiInfo);
                    Log.i(TAG, wifiJson);
                    UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnCurrentWifiReceived", wifiJson);
                } else {
                    UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnCurrentWifiReceived", "0");
                }
            }
        });
    }

    /**
     * 关机
     */
    private static void shutDown() {
        unityPlayerActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
//                if (!isRooted()) {
//                    Log.e(TAG, "Need root permission!");
//                    return;
//                }
                try {
                    // 需要 root 权限
                    java.lang.Process proc = Runtime.getRuntime().exec(new String[]{"su", "-c", "reboot -p"});
                    proc.waitFor();
                } catch (Exception e) {
                    Log.e(TAG, "Shutdown failed: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        });
    }

    private boolean isRooted() {
        try {
            java.lang.Process process = Runtime.getRuntime().exec("su");
            process.getOutputStream().write("exit\n".getBytes());
            process.getOutputStream().flush();
            int exitValue = process.waitFor();
            return exitValue == 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取wifi列表
     */
    private static void getWifiList() {
        unityPlayerActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (null != defaultWifiJson) {
                    UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiListReceived", defaultWifiJson);
                }
                if (ContextCompat.checkSelfPermission(unityPlayerActivity, Manifest.permission.ACCESS_FINE_LOCATION)
                        != PackageManager.PERMISSION_GRANTED) {
                    ActivityCompat.requestPermissions(
                            unityPlayerActivity,
                            new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                            PERMISSIONS_REQUEST_LOCATE
                    );
                } else {
                    getWifiInfoList(true);
                }
            }
        });
    }

    /**
     * 打开wifi开关
     */
    private static void openWifi() {
        unityPlayerActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (wifiManager == null) {
                    wifiManager = (WifiManager) unityPlayerActivity.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
                }
                if (!wifiManager.isWifiEnabled()) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        // Android 10及以上版本
                        Intent panelIntent = new Intent(android.provider.Settings.Panel.ACTION_WIFI);
                        unityPlayerActivity.startActivityForResult(panelIntent, WIFI_PANEL_REQUEST_CODE);
                    } else {
                        // Android 9及以下版本
                        boolean success = wifiManager.setWifiEnabled(true);
                        if (success) {
                            UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiStatusChanged", "1");
                        } else {
                            UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiStatusChanged", "0");
                        }
                    }
                } else {
                    UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiStatusChanged", "1");
                }
            }
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode == MY_PERMISSIONS_REQUEST_DOWN) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "CallBackCameraPer", "1");
            } else {
                Toast.makeText(this, "Please grant the app camera permission", Toast.LENGTH_SHORT).show();
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "CallBackCameraPer", "0");
            }
            return;
        }
        if (requestCode == PERMISSIONS_REQUEST_LOCATE) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                getWifiInfoList(true);
            } else {
                Toast.makeText(this, "Location permission is required to scan for WiFi", Toast.LENGTH_SHORT).show();
            }
            return;
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }


    private void openCameraPer() {
        Log.i(TAG, "openCameraPer");
        unityPlayerActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (ContextCompat.checkSelfPermission(unityPlayerActivity,
                        Manifest.permission.CAMERA)
                        != PackageManager.PERMISSION_GRANTED) {
                    ActivityCompat.requestPermissions(unityPlayerActivity,
                            new String[]{Manifest.permission.CAMERA},
                            MY_PERMISSIONS_REQUEST_DOWN);
                } else {
                    UnityPlayer.UnitySendMessage("AndroidSDKManager", "CallBackCameraPer", "1");
                }
                float a = 7.0f;
                float b = 2.2f;
                StringBuilder stringBuilder = new StringBuilder(String.valueOf(a));
                stringBuilder.append(",");
                stringBuilder.append(b);
                String c = stringBuilder.toString();
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "ChangeRoleSkewing", c);
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "openCameraPerOver", "1");
            }
        });
    }

    /**
     * 获取当前app版本号
     */
    private static void getAppVersion() {
        unityPlayerActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                String versionName = APKVersionCodeUtils.getVerName(unityPlayerActivity);
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnVersionReceived", versionName);
            }
        });
    }

    private static void connectToWifi(String ssid, String password) {
        unityPlayerActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    connectWifiAndroid10(ssid, password);
                } else {
                    connectWifi(ssid, password);
                }
            }
        });
    }

    private static void TouristsNativeLogin() {
        Log.i(TAG, "TouristsNativeLogin");
        unityPlayerActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                VisitInfo visitInfo = new VisitInfo();
                String platformOs = SystemUtil.getSystemModel() + "," + SystemUtil.getSystemVersion();
                String uuid = UMConfigure.getUMIDString(unityPlayerActivity);
                visitInfo.setPlatformOs(platformOs);
                String country = unityPlayerActivity.getResources().getConfiguration().locale.getCountry();
                visitInfo.setCountry(country);
                visitInfo.setDrivers("android");
                visitInfo.setPackageName("com.cy.exercisetv");
                visitInfo.setUuid(uuid);
                visitInfo.setVersion(SystemUtil.getVerName(unityPlayerActivity));
                String visitJson = new Gson().toJson(visitInfo);
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "TouristsNativeLoginSuccessCall", visitJson);
            }
        });
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    public static void connectWifiAndroid10(String ssid, String password) {
        WifiNetworkSpecifier.Builder builder = new WifiNetworkSpecifier.Builder();
        builder.setSsid(ssid);
        builder.setWpa2Passphrase(password);

        WifiNetworkSpecifier wifiNetworkSpecifier = builder.build();
        NetworkRequest.Builder networkRequestBuilder = new NetworkRequest.Builder();
        networkRequestBuilder.addTransportType(NetworkCapabilities.TRANSPORT_WIFI);
        networkRequestBuilder.setNetworkSpecifier(wifiNetworkSpecifier);
        NetworkRequest networkRequest = networkRequestBuilder.build();

        ConnectivityManager connectivityManager =
                (ConnectivityManager) unityPlayerActivity.getSystemService(Context.CONNECTIVITY_SERVICE);

        ConnectivityManager.NetworkCallback networkCallback = new ConnectivityManager.NetworkCallback() {
            @Override
            public void onAvailable(@NonNull Network network) {
                super.onAvailable(network);
                // 连接成功
                connectivityManager.bindProcessToNetwork(network);
                unityPlayerActivity.runOnUiThread(() -> {
                    UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiConnectResult", "1");
                });
            }

            @Override
            public void onUnavailable() {
                super.onUnavailable();
                // 连接失败
                unityPlayerActivity.runOnUiThread(() -> {
                    UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiConnectResult", "0");
                });
            }
        };

        connectivityManager.requestNetwork(networkRequest, networkCallback);
    }

    public static void connectWifi(String ssid, String password) {
        WifiConfiguration wifiConfig = new WifiConfiguration();
        wifiConfig.SSID = String.format("\"%s\"", ssid);
        wifiConfig.preSharedKey = String.format("\"%s\"", password);
        // 获取网络ID
        int netId = wifiManager.addNetwork(wifiConfig);
        if (netId != -1) {
            // 断开当前网络
            wifiManager.disconnect();
            // 启用新网络
            boolean enableSuccess = wifiManager.enableNetwork(netId, true);
            // 重新连接
            boolean reconnectSuccess = wifiManager.reconnect();
            // 发送连接结果到Unity
            if (enableSuccess && reconnectSuccess) {
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiConnectResult", "1");
            } else {
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiConnectResult", "0");
            }
        } else {
            UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiConnectResult", "0");
        }
    }

    private void SendImagePath(long path, int imageWidth, int imageHeight) {
//        Log.i(TAG, "SendImagePath:path："+path);
        unityPlayerActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                int size = imageWidth * imageHeight * 4;
                byte[] imageData = new byte[size];
                poseDetector.longToByteArray(path, imageData, size);
                long startTime = System.currentTimeMillis();
                PoseResult[] results = poseDetector.detectFromByteArray(imageData, imageWidth, imageHeight);
//                long endTime = System.currentTimeMillis();
//                Log.i(TAG, "time:" + (endTime - startTime));
                if (results != null && results.length > 0) {
                    StringBuilder jsonBuilder = new StringBuilder(results.length * 256);
                    jsonBuilder.append('[');
                    boolean firstPerson = true;
                    for (PoseResult result : results) {
//                        float[][] keypoints = result.getKeypoints();
                        //打印keypoints信息
//                        Log.i(TAG, "Keypoints: " + Arrays.deepToString(keypoints));
                        if (!firstPerson) {
                            jsonBuilder.append(',');
                        }
                        firstPerson = false;
                        jsonBuilder.append("{\"data\":[");
//                        jsonBuilder.append("{\"path\":").append(path)
//                                .append(",\"data\":[");
                        for (int i = 0; i < 17; i++) {
                            if (i > 0) {
                                jsonBuilder.append(',');
                            }
                            float x = result.keypoints[i][0];
                            float y = result.keypoints[i][1];
                            jsonBuilder.append("{\"x\":").append(x)
                                    .append(",\"y\":").append(y)
                                    .append('}');
                        }
                        jsonBuilder.append("]}");
                    }
                    jsonBuilder.append(']');
                    String result = jsonBuilder.toString();
                    UnityPlayer.UnitySendMessage("AndroidSDKManager", "CallBackHumanPos", result);
                    Log.i(TAG, result);
                } else {
                    UnityPlayer.UnitySendMessage("AndroidSDKManager", "CallBackHumanPos", "0");
                }
            }
        });
    }

    // When Unity player unloaded move task to background
    @Override
    public void onUnityPlayerUnloaded() {
        moveTaskToBack(true);
    }

    // When Unity player quited kill process
    @Override
    public void onUnityPlayerQuitted() {
        Process.killProcess(Process.myPid());
    }

    @Override
    protected void onNewIntent(Intent intent) {
        // To support deep linking, we need to make sure that the client can get access to
        // the last sent intent. The clients access this through a JNI api that allows them
        // to get the intent set on launch. To update that after launch we have to manually
        // replace the intent with the one caught here.
        setIntent(intent);
        mUnityPlayer.newIntent(intent);
    }

    // Quit Unity
    @Override
    protected void onDestroy() {
        mUnityPlayer.destroy();
        super.onDestroy();
        if (poseDetector != null) {
            poseDetector.release();
            poseDetector = null;
        }
        if (mHomeKeyReceiver != null) {
            unregisterReceiver(mHomeKeyReceiver);
        }
        if (wifiScanReceiver != null) {
            try {
                unregisterReceiver(wifiScanReceiver);
            } catch (IllegalArgumentException e) {
                // 接收器可能已经被注销
            }
            wifiScanReceiver = null;
        }
    }

    // If the activity is in multi window mode or resizing the activity is allowed we will use
    // onStart/onStop (the visibility callbacks) to determine when to pause/resume.
    // Otherwise it will be done in onPause/onResume as Unity has done historically to preserve
    // existing behavior.
    @Override
    protected void onStop() {
        super.onStop();

        if (!MultiWindowSupport.getAllowResizableWindow(this))
            return;

        mUnityPlayer.pause();
    }

    @Override
    protected void onStart() {
        super.onStart();

        if (!MultiWindowSupport.getAllowResizableWindow(this))
            return;

        mUnityPlayer.resume();
    }

    // Pause Unity
    @Override
    protected void onPause() {
        super.onPause();

        if (MultiWindowSupport.getAllowResizableWindow(this))
            return;

        mUnityPlayer.pause();
    }

    // Resume Unity
    @Override
    protected void onResume() {
        super.onResume();

        if (MultiWindowSupport.getAllowResizableWindow(this))
            return;

        mUnityPlayer.resume();
    }

    // Low Memory Unity
    @Override
    public void onLowMemory() {
        super.onLowMemory();
        mUnityPlayer.lowMemory();
    }

    // Trim Memory Unity
    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        if (level == TRIM_MEMORY_RUNNING_CRITICAL) {
            mUnityPlayer.lowMemory();
        }
    }

    // This ensures the layout will be correct.
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mUnityPlayer.configurationChanged(newConfig);
    }

    // Notify Unity of the focus change.
    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        mUnityPlayer.windowFocusChanged(hasFocus);
    }

    // For some reason the multiple keyevent type is not supported by the ndk.
    // Force event injection by overriding dispatchKeyEvent().
    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_MULTIPLE)
            return mUnityPlayer.injectEvent(event);
        return super.dispatchKeyEvent(event);
    }

    // Pass any events not handled by (unfocused) views straight to UnityPlayer
    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        return mUnityPlayer.injectEvent(event);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_DPAD_CENTER:
            case KeyEvent.KEYCODE_ENTER:
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "ClickEnterMyAction", "");
                return true;
            default:
                return mUnityPlayer.injectEvent(event);
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return mUnityPlayer.injectEvent(event);
    }

    /*API12*/
    public boolean onGenericMotionEvent(MotionEvent event) {
        return mUnityPlayer.injectEvent(event);
    }

    @Override
    public void getRequestMsg(int type, String msg) {
        if (type == 1) {
            Message message = new Message();
            message.what = 1;
            Bundle mBundle = new Bundle();
            mBundle.putString("msg", msg);
            message.setData(mBundle);
            handler.sendMessage(message);
        }
    }

    private static Handler handler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case 1:
                    Bundle bundle = msg.getData();
                    String tag = bundle.getString("msg");
                    Gson gson = new Gson();
                    VersionInfo versionInfo = gson.fromJson(tag, VersionInfo.class);
                    if (versionInfo.getError() == 0) {
                        String version = versionInfo.getData().getVersion();
                        if (!version.equals(APKVersionCodeUtils.getVerName(unityPlayerActivity)) && versionInfo.getData().getVersion_status() == 1) {
                            boolean isForce = versionInfo.getData().getIf_forced_update() == 1 ? true : false;
                            UpdateManager updateManager = new UpdateManager(unityPlayerActivity);
                            updateManager.setDownloadUrl(versionInfo.getData().getApk_url());
                            updateManager.checkUpdate(isForce);
                        }
                    } else {
                    }
                    break;
                default:
                    break;
            }
        }
    };

    private BroadcastReceiver mHomeKeyReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction().equals(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)) {
                String reason = intent.getStringExtra("reason");
                if ("homekey".equals(reason)) {
                    UnityPlayer.UnitySendMessage("AndroidSDKManager", "ClickEnterHome", "");
                }
            }
        }
    };

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == WIFI_PANEL_REQUEST_CODE) {
            // 检查 WiFi 是否已启用
            if (null != wifiManager && wifiManager.isWifiEnabled()) {
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiStatusChanged", "1");
            } else {
                UnityPlayer.UnitySendMessage("AndroidSDKManager", "OnWifiStatusChanged", "0");
            }
        }
    }
}
